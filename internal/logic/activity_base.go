package logic

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// ActivityHandler 活动处理器接口 - 简单的通用接口，避免过度设计
type ActivityHandler interface {
	// GetActivityId 获取活动ID
	GetActivityId() int64
	
	// HandleEvent 处理事件
	HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error
	
	// GetProgress 获取活动进度
	GetProgress(ctx context.Context, playerId uint64) (interface{}, error)
	
	// ClaimReward 领取奖励
	ClaimReward(ctx context.Context, playerId uint64, params map[string]interface{}) error
	
	// CheckRedDot 检查红点
	CheckRedDot(ctx context.Context, playerId uint64) (bool, error)
}

// ActivityManager 活动管理器 - 简单的注册和分发机制
type ActivityManager struct {
	handlers map[int64]ActivityHandler
}

// NewActivityManager 创建活动管理器
func NewActivityManager() *ActivityManager {
	return &ActivityManager{
		handlers: make(map[int64]ActivityHandler),
	}
}

// RegisterHandler 注册活动处理器
func (am *ActivityManager) RegisterHandler(handler ActivityHandler) {
	am.handlers[handler.GetActivityId()] = handler
}

// HandleEvent 分发事件到对应的活动处理器
func (am *ActivityManager) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	// 遍历所有处理器，让它们自己判断是否需要处理这个事件
	for _, handler := range am.handlers {
		// 忽略错误，继续处理其他活动
		_ = handler.HandleEvent(ctx, playerId, event)
	}
}

// GetProgress 获取指定活动的进度
func (am *ActivityManager) GetProgress(ctx context.Context, activityId int64, playerId uint64) (interface{}, error) {
	if handler, exists := am.handlers[activityId]; exists {
		return handler.GetProgress(ctx, playerId)
	}
	return nil, nil
}

// ClaimReward 领取指定活动的奖励
func (am *ActivityManager) ClaimReward(ctx context.Context, activityId int64, playerId uint64, params map[string]interface{}) error {
	if handler, exists := am.handlers[activityId]; exists {
		return handler.ClaimReward(ctx, playerId, params)
	}
	return nil
}

// CheckRedDot 检查指定活动的红点
func (am *ActivityManager) CheckRedDot(ctx context.Context, activityId int64, playerId uint64) (bool, error) {
	if handler, exists := am.handlers[activityId]; exists {
		return handler.CheckRedDot(ctx, playerId)
	}
	return false, nil
}

// CheckAllRedDots 检查所有活动的红点
func (am *ActivityManager) CheckAllRedDots(ctx context.Context, playerId uint64) (bool, error) {
	for _, handler := range am.handlers {
		hasRedDot, err := handler.CheckRedDot(ctx, playerId)
		if err != nil {
			continue // 忽略错误，继续检查其他活动
		}
		if hasRedDot {
			return true, nil
		}
	}
	return false, nil
}
