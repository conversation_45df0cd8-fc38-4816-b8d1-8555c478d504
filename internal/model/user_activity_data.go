package model

import (
	"encoding/json"
	"time"
)

// UserActivityData 玩家活动数据结构
type UserActivityData struct {
	Metrics        map[int32]int64  `json:"metrics"`         // 累加类型的指标值，key为指标类型，value为数值
	MetricsMax     map[int32]int64  `json:"metrics_max"`     // 最大值类型的指标，key为指标类型，value为最大值
	MetricsExtra   map[int32]string `json:"metrics_extra"`   // 扩展字段，key为指标类型，value为JSON字符串
	ClaimedStages  map[int32]int64  `json:"claimed_stages"`  // 已领取阶段记录，key为阶段ID，value为领取时间戳
	RewardConfigs  map[int32]string `json:"reward_configs"`  // 奖励配置记录，key为阶段ID，value为奖励配置JSON
	UpdatedAt      int64            `json:"updated_at"`      // 最后更新时间戳
	Version        string           `json:"version"`         // 数据版本号，用于兼容性管理
}

// MetricOperationType 指标操作类型
type MetricOperationType int32

const (
	MetricOperationAdd MetricOperationType = 1 // 累加操作
	MetricOperationMax MetricOperationType = 2 // 最大值操作
	MetricOperationSet MetricOperationType = 3 // 直接设置
)

// NewUserActivityData 创建新的用户活动数据
func NewUserActivityData() *UserActivityData {
	return &UserActivityData{
		Metrics:       make(map[int32]int64),
		MetricsMax:    make(map[int32]int64),
		MetricsExtra:  make(map[int32]string),
		ClaimedStages: make(map[int32]int64),
		RewardConfigs: make(map[int32]string),
		UpdatedAt:     time.Now().Unix(),
		Version:       "v2.0",
	}
}

// ToJSON 转换为JSON字符串
func (uad *UserActivityData) ToJSON() (string, error) {
	data, err := json.Marshal(uad)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (uad *UserActivityData) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), uad)
}

// UpdateMetric 更新指标值
func (uad *UserActivityData) UpdateMetric(metricType int32, value int64, operation MetricOperationType) {
	switch operation {
	case MetricOperationAdd:
		if uad.Metrics == nil {
			uad.Metrics = make(map[int32]int64)
		}
		uad.Metrics[metricType] += value
	case MetricOperationMax:
		if uad.MetricsMax == nil {
			uad.MetricsMax = make(map[int32]int64)
		}
		if value > uad.MetricsMax[metricType] {
			uad.MetricsMax[metricType] = value
		}
	case MetricOperationSet:
		if uad.Metrics == nil {
			uad.Metrics = make(map[int32]int64)
		}
		uad.Metrics[metricType] = value
	}
	uad.UpdatedAt = time.Now().Unix()
}

// GetMetricValue 获取指标值
func (uad *UserActivityData) GetMetricValue(metricType int32) int64 {
	if value, exists := uad.Metrics[metricType]; exists {
		return value
	}
	if value, exists := uad.MetricsMax[metricType]; exists {
		return value
	}
	return 0
}

// ClaimStage 领取阶段奖励
func (uad *UserActivityData) ClaimStage(stageId int32, rewardConfig string) {
	if uad.ClaimedStages == nil {
		uad.ClaimedStages = make(map[int32]int64)
	}
	if uad.RewardConfigs == nil {
		uad.RewardConfigs = make(map[int32]string)
	}
	
	now := time.Now().Unix()
	uad.ClaimedStages[stageId] = now
	uad.RewardConfigs[stageId] = rewardConfig
	uad.UpdatedAt = now
}

// IsStageClaimedStage 检查阶段是否已领取
func (uad *UserActivityData) IsStageClaimedStage(stageId int32) bool {
	if uad.ClaimedStages == nil {
		return false
	}
	_, exists := uad.ClaimedStages[stageId]
	return exists
}

// GetClaimedStagesList 获取已领取阶段列表
func (uad *UserActivityData) GetClaimedStagesList() []int32 {
	if uad.ClaimedStages == nil {
		return []int32{}
	}
	
	stages := make([]int32, 0, len(uad.ClaimedStages))
	for stageId := range uad.ClaimedStages {
		stages = append(stages, stageId)
	}
	return stages
}

// EnsureInitialized 确保数据结构已初始化
func (uad *UserActivityData) EnsureInitialized() {
	if uad.Metrics == nil {
		uad.Metrics = make(map[int32]int64)
	}
	if uad.MetricsMax == nil {
		uad.MetricsMax = make(map[int32]int64)
	}
	if uad.MetricsExtra == nil {
		uad.MetricsExtra = make(map[int32]string)
	}
	if uad.ClaimedStages == nil {
		uad.ClaimedStages = make(map[int32]int64)
	}
	if uad.RewardConfigs == nil {
		uad.RewardConfigs = make(map[int32]string)
	}
	if uad.Version == "" {
		uad.Version = "v2.0"
	}
}

// GetClaimedStagesCount 获取已领取阶段数量
func (uad *UserActivityData) GetClaimedStagesCount() int {
	if uad.ClaimedStages == nil {
		return 0
	}
	return len(uad.ClaimedStages)
}

// HasAnyMetrics 检查是否有任何指标数据
func (uad *UserActivityData) HasAnyMetrics() bool {
	return len(uad.Metrics) > 0 || len(uad.MetricsMax) > 0
}

// GetTotalMetricValue 获取指标总值（累加类型）
func (uad *UserActivityData) GetTotalMetricValue() int64 {
	var total int64
	for _, value := range uad.Metrics {
		total += value
	}
	return total
}
